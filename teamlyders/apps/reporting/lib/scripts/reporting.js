TL.Ready(["Downloader"], function () {
	TL.AddScript("resource?src=scripts/reporting-shared.js", function () {
		// Define global state
		const State = {
			CurrentCategory: null,
			ReportData: {},
			FavoriteReports: [],
			ReportCategories: [],
			CurrentReportId: null, // for single report view
		};

		// Navigation Object To Handle URL And Routing
		const Navigation = {
			GetCategoryFromURL: () => {
				const Category = TL.Location.URLParams("category");
				return Category && State.ReportData[Category] ? Category : null;
			},
			GetReportIdFromURL: () => {
				const Id = TL.Location.URLParams("id");
				return Id ? parseInt(Id, 10) : null;
			},
			UpdateURL: (CategoryKey) => {
				if (!CategoryKey) return;
				TL.Browser.EditURL(`reporting?category=${CategoryKey}`);
			},
			UpdateURLWithReport: (ReportId) => {
				if (!ReportId) return;
				TL.Browser.EditURL(`reporting?id=${ReportId}`);
			},
			GetDefaultCategory: () => {
				// Return the first available category from the fetched data (excluding favorites)
				const Categories = Object.keys(State.ReportData).filter((cat) => cat !== "favorites");
				return Categories.length > 0 ? Categories[0] : "favorites";
			},
		};

		// DOM Elements (resolved at runtime in InitializeReporting to handle scripts loaded before markup)
		const Elements = {
			AppWrapper: null,
			FavoritesList: null,
			FavoritesCount: null,
			CategoryList: null,
			CategoryTitle: null,
			CategoryDescription: null,
			ReportsGrid: null,
			EmptyState: null,
			MainContent: null,
		};

		// Loader helper (mirrors pattern used by the benefits app)
		const Loader = {
			Start: (Container = "#reportingAppContainer") => {
				TL.Loading.Start(Container);
			},
			Stop: (Container = "#reportingAppContainer") => {
				TL.Loading.Stop(Container);
			},
		};

		// Small show/hide helpers (accept selector string, Element, NodeList or Array)
		function ShowElement(Target) {
			if (!Target) return;
			const Apply = (El) => {
				if (!El) return;
				El.classList.remove("hidden");
			};

			if (typeof Target === "string") {
				const Nodes = document.querySelectorAll(Target);
				Nodes.forEach(Apply);
				return;
			}

			if (NodeList.prototype.isPrototypeOf(Target) || Array.isArray(Target)) {
				Target.forEach(Apply);
				return;
			}

			Apply(Target);
		}

		function HideElement(Target) {
			if (!Target) return;
			const Apply = (El) => {
				if (!El) return;
				El.classList.add("hidden");
			};

			if (typeof Target === "string") {
				const Nodes = document.querySelectorAll(Target);
				Nodes.forEach(Apply);
				return;
			}

			if (NodeList.prototype.isPrototypeOf(Target) || Array.isArray(Target)) {
				Target.forEach(Apply);
				return;
			}

			Apply(Target);
		}

		// Initialize The Reporting App
		InitializeReporting();

		/*
		 **
		 ** Initialize Reporting App
		 ** ========================
		 ** Fetch reports, favorites and categories in parallel (non-blocking)
		 ** and perform rendering / event wiring in a final block so the UI
		 ** becomes usable even if one of the agents returns an error.
		 */
		function InitializeReporting() {
			// Start loader and mark app as busy before any async work
			Loader.Start();

			// Resolve DOM elements now that the parser should have created them
			Elements.AppWrapper = document.querySelector(".reporting-app");
			Elements.MainContent = document.querySelector(".main-content");
			Elements.FavoritesList = document.getElementById("favoritesList");
			Elements.FavoritesCount = document.getElementById("favoritesCount");
			Elements.CategoryList = document.getElementById("categoryList");
			Elements.CategoryTitle = document.getElementById("categoryTitle");
			Elements.CategoryDescription = document.getElementById("categoryDescription");
			Elements.ReportsGrid = document.getElementById("reportsGrid");
			Elements.EmptyState = document.getElementById("emptyState");

			// Start fetching reports, favorites and categories in parallel.
			const FetchReportsPromise = GetReports();
			const FetchFavoritesPromise = GetFavorites();
			const FetchReportCategoriesPromise = GetReportCategories();

			// Use Promise.allSettled so we can handle each result and still
			// proceed to render the UI regardless of individual failures.
			Promise.allSettled([FetchReportsPromise, FetchFavoritesPromise, FetchReportCategoriesPromise])
				.then((results) => {
					const [reportsRes, favoritesRes, categoriesRes] = results;

					// Report categories (process first so they're available for reports processing)
					if (categoriesRes.status === "fulfilled") {
						TL.DebugLog("Report categories retrieved:", categoriesRes.value);
						State.ReportCategories = categoriesRes.value || [];
					} else if (categoriesRes) {
						TL.Notify.Banner("Error", "Failed to retrieve report categories");
						TL.DebugLog("GetReportCategories failed:", categoriesRes.reason);
						State.ReportCategories = [];
					}

					// Reports (process after categories are loaded)
					if (reportsRes.status === "fulfilled") {
						try {
							State.ReportData = ProcessReportsData(reportsRes.value || {});
							TL.DebugLog("Reports retrieved:", reportsRes.value);
						} catch (err) {
							TL.DebugLog("Error processing reports data:", err);
							State.ReportData = ProcessReportsData({});
						}
					} else {
						TL.DebugLog("GetReports failed:", reportsRes.reason);
						State.ReportData = ProcessReportsData({});
					}

					// Favorites
					if (favoritesRes.status === "fulfilled") {
						const ServerFavorites = favoritesRes.value;
						if (Array.isArray(ServerFavorites) && ServerFavorites.length > 0) {
							// Server returns full report rows; store just the IDs for consistency
							State.FavoriteReports = ServerFavorites.map((r) => r.ID);
						} else {
							TL.DebugLog("No favorites found on server, initializing empty state");
							State.FavoriteReports = [];
						}
					} else {
						TL.DebugLog("GetFavorites failed:", favoritesRes.reason);
						State.FavoriteReports = [];
					}

					// Determine initial category after report data is available
					let InitialCategory = Navigation.GetCategoryFromURL();
					const RequestedReportId = Navigation.GetReportIdFromURL();
					if (!InitialCategory || !State.ReportData[InitialCategory]) {
						InitialCategory = Navigation.GetDefaultCategory();
					}
					State.CurrentCategory = InitialCategory;
					State.CurrentReportId = RequestedReportId;
				})
				.finally(() => {
					// Render and wire UI after attempts to fetch data
					RenderCategoryList();
					UpdateFavoritesData();
					UpdateFavoritesCount();

					// Reveal category list now that it's populated
					if (Elements.CategoryList) ShowElement(Elements.CategoryList);

					// Set Up Initial State
					SetActiveCategory(State.CurrentCategory, true); // Skip URL Update On First Load
					if (State.CurrentReportId) {
						Loader.Start();
						ShowReportView(State.CurrentReportId);
					} else {
						LoadCategory(State.CurrentCategory);
					}
					InitializeEventListeners();

					// Stop loading and show app
					Loader.Stop();
					if (Elements.AppWrapper) {
						ShowElement(Elements.MainContent);
						ShowElement(Elements.AppWrapper);
					}
				});
		}

		/*
		 **
		 ** Initialize Event Listeners
		 ** ==========================
		 */
		function InitializeEventListeners() {
			// Setup Favorites List Event Listeners
			if (Elements.FavoritesList) {
				const FavoritesItems = Elements.FavoritesList.querySelectorAll(".category-item");
				FavoritesItems.forEach((Item) => {
					Item.addEventListener("click", function () {
						const CategoryKey = this.dataset.category;
						HandleCategoryClick(CategoryKey);
					});
				});
			}

			// Setup Main Category List Event Listeners (for dynamically created categories)
			AttachCategoryEventListeners();

			// Initialize mobile sidebar behavior (toggle + auto-close on selection)
			InitializeMobileSidebar();
		}

		/*
		 **
		 ** Initialize Mobile Sidebar Toggle
		 ** =================================
		 */
		function InitializeMobileSidebar() {
			const Toggle = document.getElementById("sidebarToggle");
			const Close = document.getElementById("sidebarClose");
			const Sidebar = document.querySelector(".sidebar");
			const Backdrop = document.getElementById("sidebarBackdrop");
			const CategoryItems = Elements.CategoryList ? Elements.CategoryList.querySelectorAll(".category-item") : document.querySelectorAll(".category-item");

			if (!Toggle || !Sidebar || !Backdrop) return;

			const OpenSidebar = () => {
				Sidebar.classList.add("open");
				Backdrop.classList.add("show");
				Toggle.setAttribute("aria-expanded", "true");
				Backdrop.setAttribute("aria-hidden", "false");
			};

			const CloseSidebar = () => {
				Sidebar.classList.remove("open");
				Backdrop.classList.remove("show");
				Toggle.setAttribute("aria-expanded", "false");
				Backdrop.setAttribute("aria-hidden", "true");
			};

			Toggle.addEventListener("click", (e) => {
				e.stopPropagation();
				if (Sidebar.classList.contains("open")) CloseSidebar();
				else OpenSidebar();
			});

			// Close button event listener
			if (Close) {
				Close.addEventListener("click", (e) => {
					e.stopPropagation();
					CloseSidebar();
				});
			}

			Backdrop.addEventListener("click", CloseSidebar);

			// Close sidebar when a category is clicked (mobile)
			CategoryItems.forEach((Item) => {
				Item.addEventListener("click", () => {
					if (window.innerWidth <= 900) CloseSidebar();
				});
			});
		}

		/*
		 **
		 ** Process Reports Data From API
		 ** ============================
		 */
		function ProcessReportsData(ApiData) {
			TL.DebugLog("Processing API Data:", ApiData);

			// Initialize with favorites category
			const ProcessedData = {
				favorites: {
					Title: "Favorites",
					Description: "Your favorite reports for quick access",
					Reports: [],
				},
			};

			// Helper function to get category info from ReportCategories
			const GetCategoryInfo = (CategoryKey) => {
				const CategoryData = State.ReportCategories.find((cat) => cat.Name && cat.Name.toLowerCase() === CategoryKey.toLowerCase());

				if (CategoryData) {
					return {
						Title: CategoryData.Name,
						Description: CategoryData.Description || `${CategoryData.Name} reports and analytics`,
					};
				}

				// Fallback if category not found in ReportCategories
				const CategoryName = CategoryKey.charAt(0).toUpperCase() + CategoryKey.slice(1).toLowerCase();
				return {
					Title: CategoryName,
					Description: `${CategoryName} reports and analytics`,
				};
			};

			// Process each category from the API
			Object.keys(ApiData).forEach((CategoryKey) => {
				const CategoryReports = ApiData[CategoryKey];
				if (!Array.isArray(CategoryReports) || CategoryReports.length === 0) return;

				// Get category info from ReportCategories or use fallback
				const CategoryInfo = GetCategoryInfo(CategoryKey);

				ProcessedData[CategoryKey.toLowerCase()] = {
					Title: CategoryInfo.Title,
					Description: CategoryInfo.Description,
					Reports: CategoryReports.map((Report) => ({
						ID: Report.ID,
						Name: Report.Name,
						DisplayName: Report["Display Name"] || Report.Name,
						Description: Report.Description || "No description available",
						Icon: GetReportIcon(Report.Icon || "📊"),
						Category: Report.Category,
						Format: Report.Format,
						Version: Report.Version,
					})),
				};
			});

			return ProcessedData;
		}

		/*
		 **
		 ** Get Report Icon
		 ** ==============
		 */
		function GetReportIcon(IconName) {
			const IconMap = {
				building: "🏢",
				alert: "⚠️",
				contact: "�",
				"people-group": "👥",
				default: "📊",
			};
			return IconMap[IconName] || IconMap.default;
		}

		/*
		 **
		 ** Render Category List
		 ** ===================
		 */
		function RenderCategoryList() {
			const CategoryList = Elements.CategoryList;
			if (!CategoryList) return;

			// Clear existing categories (except favorites which is handled separately)
			CategoryList.innerHTML = "";

			// Get categories excluding favorites
			const Categories = Object.keys(State.ReportData).filter((key) => key !== "favorites");

			// Render each category
			Categories.forEach((CategoryKey) => {
				const Category = State.ReportData[CategoryKey];
				const ReportCount = Category.Reports.length;

				const TemplateData = {
					Category: CategoryKey,
					Name: Category.Title,
					Count: ReportCount,
					ActiveClass: CategoryKey === State.CurrentCategory ? "active" : "",
				};

				const Template = TL.Template("Category-Item");
				if (Template) {
					const FilledTemplate = TL.FillTemplate(Template, TemplateData);
					const TempDiv = document.createElement("div");
					TempDiv.innerHTML = FilledTemplate;
					const CategoryItem = TempDiv.firstElementChild;

					if (CategoryItem) {
						CategoryList.appendChild(CategoryItem);
					}
				}
			});

			// Re-attach event listeners for new category items
			AttachCategoryEventListeners();
		}
		/*
		 **
		 ** Attach Category Event Listeners
		 ** ==============================
		 */
		function AttachCategoryEventListeners() {
			// Setup Main Category List Event Listeners
			if (Elements.CategoryList) {
				const CategoryItems = Elements.CategoryList.querySelectorAll(".category-item");
				CategoryItems.forEach((Item) => {
					Item.addEventListener("click", function () {
						const CategoryKey = this.dataset.category;
						HandleCategoryClick(CategoryKey);
					});
				});
			}
		}

		/*
		 **
		 ** Handle Category Click
		 ** ====================
		 */
		function HandleCategoryClick(CategoryKey) {
			if (!CategoryKey || CategoryKey === State.CurrentCategory) return;

			State.CurrentCategory = CategoryKey;
			SetActiveCategory(CategoryKey);
			LoadCategory(CategoryKey);
		}

		/*
		 **
		 ** Set Active Category Visual State
		 ** ===============================
		 */
		function SetActiveCategory(CategoryKey, SkipUrl = false) {
			if (!CategoryKey) return;

			// Remove Active State From All Category Items In Both Lists
			const AllCategoryItems = [...(Elements.FavoritesList?.querySelectorAll(".category-item") || []), ...(Elements.CategoryList?.querySelectorAll(".category-item") || [])];

			// Remove Active From All Items First
			AllCategoryItems.forEach((Item) => {
				Item.classList.remove("active");
			});

			// Add Active State To The Matching Category
			const TargetItem = AllCategoryItems.find((Item) => Item.dataset.category === CategoryKey);
			if (TargetItem) {
				TargetItem.classList.add("active");
			}

			// Update URL If Not Skipped
			if (!SkipUrl) Navigation.UpdateURL(CategoryKey);
		}

		/*
		 **
		 ** Load Reports For Category
		 ** ========================
		 */
		function LoadCategory(CategoryKey) {
			TL.DebugLog("Loading Category:", CategoryKey);

			const Category = State.ReportData[CategoryKey];
			if (!Category) {
				ShowEmptyState();
				return;
			}

			// Update Page Header
			UpdateCategoryHeader(Category);

			// Clear And Populate Reports
			ClearReportsGrid();

			if (Category.Reports.length === 0) {
				ShowEmptyState();
				return;
			}

			RenderReports(Category.Reports, CategoryKey);
		}

		/*
		 **
		 ** Update Category Header
		 ** =====================
		 */
		function UpdateCategoryHeader(Category) {
			TL.DebugLog("Updating Category Header:", Category);
			Elements.CategoryTitle.textContent = Category.Title;
			Elements.CategoryDescription.textContent = Category.Description;
		}

		/*
		 **
		 ** Clear Reports Grid
		 ** =================
		 */
		function ClearReportsGrid() {
			if (Elements.ReportsGrid) {
				Elements.ReportsGrid.innerHTML = "";
				HideElement(Elements.ReportsGrid);
			}
		}

		/*
		 **
		 ** Render Reports
		 ** =============
		 */
		function RenderReports(Reports, CategoryKey) {
			// Hide Empty State And Show Reports Grid
			if (Elements.EmptyState) HideElement(Elements.EmptyState);
			if (Elements.ReportsGrid) {
				ShowElement(Elements.ReportsGrid);
			}

			// Create And Append Report Cards
			Reports.forEach((Report) => {
				const ReportCard = CreateReportCard(Report, CategoryKey);
				if (Elements.ReportsGrid && ReportCard) {
					Elements.ReportsGrid.appendChild(ReportCard);
				}
			});
		}

		/*
		 **
		 ** Create Report Card Element
		 ** =========================
		 */
		function CreateReportCard(Report, CategoryKey) {
			// Handle "No Description Available" Cases
			const DescriptionClass = Report.Description === "No description available" ? "no-description" : "";

			// Check If Report Is Favorited
			const ReportId = Report.ID;
			const IsFavorited = State.FavoriteReports.includes(ReportId);
			const FavoriteClass = IsFavorited ? "favorited" : "";
			const FavoriteTitle = IsFavorited ? "Remove from favorites" : "Add to favorites";

			// Prepare Template Data
			const TemplateData = {
				Category: CategoryKey,
				Icon: Report.Icon,
				Title: Report.DisplayName || Report.Name,
				Description: Report.Description,
				DescriptionClass: DescriptionClass,
				FavoriteClass: FavoriteClass,
				FavoriteTitle: FavoriteTitle,
			};

			// Get Template And Fill With Data
			const Template = TL.Template("Report-Card");
			if (!Template) {
				TL.DebugLog("Template 'Report-Card' not found");
				return null;
			}

			const FilledTemplate = TL.FillTemplate(Template, TemplateData);
			if (!FilledTemplate) {
				TL.DebugLog("Failed to fill template for report:", Report.Name);
				return null;
			}

			// Create DOM Element
			const TempDiv = document.createElement("div");
			TempDiv.innerHTML = FilledTemplate;
			const Card = TempDiv.firstElementChild;

			if (!Card) {
				TL.DebugLog("Failed to create card element for report:", Report.Name);
				return null;
			}

			// Add Event Listeners
			AttachReportCardEvents(Card, Report, CategoryKey);

			return Card;
		}

		/*
		 **
		 ** Attach Report Card Event Listeners
		 ** =================================
		 */
		function AttachReportCardEvents(Card, Report) {
			if (!Card) {
				TL.DebugLog("Cannot attach events: card is null for report:", Report.Name);
				return;
			}

			// Generate Report Button
			const GenerateBtn = Card.querySelector(".generate-btn");
			if (GenerateBtn) {
				GenerateBtn.addEventListener("click", function () {
					// Use the report Name for the API call (this is the actual report identifier)
					GenerateReport(Report.Name, Report);
				});
			}

			// Favorite Button
			const FavoriteBtn = Card.querySelector(".favorite-btn");
			if (FavoriteBtn) {
				FavoriteBtn.addEventListener("click", function () {
					ToggleFavorite(FavoriteBtn, Report);
				});
			}

			// View Button
			const ViewBtn = Card.querySelector(".view-btn");
			if (ViewBtn) {
				ViewBtn.addEventListener("click", function () {
					ShowReportView(Report.ID, Report, { showLoader: true });
				});
			}
		}

		/*
		 ** Build Report Lookup Map
		 */
		function GetReportById(ReportId) {
			if (!ReportId) return null;
			// Search across categories
			for (const CatKey of Object.keys(State.ReportData)) {
				const Cat = State.ReportData[CatKey];
				if (!Cat || !Array.isArray(Cat.Reports)) continue;
				const Match = Cat.Reports.find((r) => r.ID === ReportId);
				if (Match) return Match;
			}
			return null;
		}

		/*
		 ** Show Single Report View
		 */
		function ShowReportView(ReportId, ReportObj, { showLoader = false } = { showLoader: false }) {
			// Start loader for table data fetching
			if (showLoader) Loader.Start();

			const Report = ReportObj || GetReportById(ReportId);
			if (!Report) {
				TL.Notify && TL.Notify.Banner && TL.Notify.Banner("Not Found", "Report not found");
				return;
			}

			// Track current view and update URL
			State.CurrentReportId = ReportId;
			Navigation.UpdateURLWithReport(ReportId);

			// Toggle UI into single-report mode
			HideElement(Elements.ReportsGrid);
			HideElement(Elements.EmptyState);
			if (Elements.AppWrapper) Elements.AppWrapper.classList.add("single-report-mode");
			// Build report view template
			const ViewTemplate = TL.Template("Report-View");
			if (!ViewTemplate) return;

			// Show a small loading placeholder for the table; real table will be
			// populated by FetchReportTableData which calls BuildDummyReportTable.
			const CatLower = (Report.Category || "").toLowerCase();
			const Filled = TL.FillTemplate(ViewTemplate, {
				Title: Report.DisplayName || Report.Name,
				Description: Report.Description || "Report details",
				InternalName: Report.Name,
				Category: CatLower,
				Icon: Report.Icon || "📊",
				FavoriteClass: State.FavoriteReports.includes(Report.ID) ? "favorited" : "",
				FavoriteTitle: State.FavoriteReports.includes(Report.ID) ? "Remove from favorites" : "Add to favorites",
				Table: '<div class="report-table-loading">Loading table…</div>',
			});

			// Remove any existing view
			const Existing = Elements.MainContent.querySelector(".report-view");
			if (Existing) Existing.remove();

			// Insert at top of main content after header
			const Header = Elements.MainContent.querySelector(".category-header");
			const WrapperDiv = document.createElement("div");
			WrapperDiv.innerHTML = Filled;
			const ViewEl = WrapperDiv.firstElementChild;
			if (Header && ViewEl) {
				Header.after(ViewEl);
			} else if (Elements.MainContent && ViewEl) {
				Elements.MainContent.appendChild(ViewEl);
			}

			// Wire back & generate inside view
			const BackBtn = ViewEl.querySelector(".back-btn");
			if (BackBtn) {
				BackBtn.addEventListener("click", () => {
					HideReportView();
				});
			}

			// Header favorite toggle
			const HeaderFav = ViewEl.querySelector(".favorite-btn");
			if (HeaderFav) {
				HeaderFav.addEventListener("click", () => {
					ToggleFavorite(HeaderFav, Report);
					// Sync footer favorite if list returns later
				});
			}
			// Sticky header compression on scroll
			const HeaderEl = ViewEl.querySelector(".report-view-header");
			if (HeaderEl) {
				const OnScroll = () => {
					const scrolled = window.scrollY || document.documentElement.scrollTop;
					if (scrolled > 40) {
						HeaderEl.classList.add("header-condensed");
					} else {
						HeaderEl.classList.remove("header-condensed");
					}
				};
				window.addEventListener("scroll", OnScroll, { passive: true });
				// Remove listener when view removed
				HeaderEl.addEventListener("cleanup", () => window.removeEventListener("scroll", OnScroll));
			}
			const InnerGenerate = ViewEl.querySelector(".generate-btn");
			if (InnerGenerate) InnerGenerate.addEventListener("click", () => GenerateReport(Report.Name, Report));

			// Fetch table data asynchronously
			FetchReportTableData(Report)
				.then((TableHTML) => {
					// Render the table HTML into the view
					RenderReportTable(TableHTML, ViewEl);
					// Initialize search filtering for the rendered table
					InitializeReportTableSearch(ViewEl);
					// Initialize advanced filter (date range) for the rendered table
					InitializeReportFilters(ViewEl);
					// Stop loader after table is rendered
					if (showLoader) Loader.Stop();
				})
				.catch((err) => {
					TL.DebugLog("FetchReportTableData failed:", err);
					// Stop loader on error
					if (showLoader) Loader.Stop();
				});
		}

		/*
		 ** Hide Single Report View & Return To List
		 */
		function HideReportView() {
			State.CurrentReportId = null;
			// Update URL back to category
			Navigation.UpdateURL(State.CurrentCategory || Navigation.GetDefaultCategory());
			const Existing = Elements.MainContent.querySelector(".report-view");
			if (Existing) {
				const HeaderEl = Existing.querySelector(".report-view-header");
				if (HeaderEl) HeaderEl.dispatchEvent(new Event("cleanup"));
				Existing.remove();
			}
			if (Elements.AppWrapper) Elements.AppWrapper.classList.remove("single-report-mode");
			LoadCategory(State.CurrentCategory);
		}

		/*
		 ** Extract Table Data From Array of Objects
		 ** =======================================
		 ** Utility function to extract headers and rows from an array of objects
		 */
		function ExtractTableData(DataArray) {
			if (!Array.isArray(DataArray) || DataArray.length === 0) {
				return { Headers: [], Rows: [] };
			}

			// Extract headers from the first object's keys
			const Headers = Object.keys(DataArray[0]);

			// Extract rows by mapping over each object and getting its values in header order
			const Rows = DataArray.map((rowData) => {
				return Headers.map((header) => {
					const value = rowData[header];
					// Handle different data types appropriately
					if (value === null || value === undefined) {
						return "";
					}
					if (typeof value === "number") {
						return value.toString();
					}
					if (typeof value === "boolean") {
						return value ? "Yes" : "No";
					}
					return String(value);
				});
			});

			return { Headers, Rows };
		}

		/*
		 **
		 ** Fetch Report Table Data
		 ** =====================
		 ** Fetches real report data using the ["app", "retrieve"] agent
		 ** and builds the table from the returned data array
		 */
		function FetchReportTableData(Report) {
			return new Promise((resolve, reject) => {
				try {
					TL.DebugLog("FetchReportTableData requested for report:", Report && (Report.Name || Report.ID));

					// Call the agent to retrieve actual report data
					TL.Agent({
						agent: ["app", "retrieve"],
						data: {
							ReportName: Report.Name,
						},
						success(ApiData) {
							TL.DebugLog("Report data retrieved from agent:", ApiData);

							let TableHTML;
							// Check if we got valid data
							if (Array.isArray(ApiData) && ApiData.length > 0) {
								// Use the real data to build the table
								TableHTML = BuildReportTableFromData(ApiData, Report);
								TL.DebugLog("Table built from real data for report:", Report.Name);
							} else {
								// If no data or invalid format, show empty state message
								TL.DebugLog("No valid data returned for report:", Report.Name);
								TableHTML = '<div class="report-table-empty">No data available for this report.</div>';
							}

							resolve(TableHTML);
						},
						error(error) {
							TL.DebugLog("Agent retrieve failed for report:", Report.Name, error);
							// On error, show error message instead of dummy data
							const TableHTML = '<div class="report-table-error">Failed to load report data. Please try again.</div>';

							resolve(TableHTML);
						},
					});
				} catch (err) {
					TL.DebugLog("FetchReportTableData error:", err);
					// Show error message on any error
					const TableHTML = '<div class="report-table-error">An error occurred while loading the report.</div>';
					resolve(TableHTML);
				}
			});
		}

		/*
		 ** Build Report Table From Real Data
		 ** ================================
		 ** This function takes an array of objects (like your sample data)
		 ** and builds a table using the ExtractTableData utility
		 */
		function BuildReportTableFromData(DataArray, Report) {
			if (!Array.isArray(DataArray) || DataArray.length === 0) {
				return '<div class="report-table-empty">No data available for this report.</div>';
			}

			// Use the utility function to extract headers and rows
			const { Headers, Rows } = ExtractTableData(DataArray);

			if (Headers.length === 0) {
				return '<div class="report-table-empty">No valid data structure found.</div>';
			}

			// Build the table using the same template pattern as BuildDummyReportTable
			const TableTemplate = TL.Template("Report-Table");
			if (!TableTemplate) return "";

			// Parse the template into DOM so we can safely manipulate the thead/tbody
			const TempDiv = document.createElement("div");
			TempDiv.innerHTML = TableTemplate;
			const TableEl = TempDiv.querySelector("table");
			if (!TableEl) {
				TL.DebugLog("Report-Table template does not contain a <table> element");
				return TempDiv.innerHTML;
			}

			// Find header row container and tbody
			const TheadTr = TableEl.querySelector("thead tr");
			const Tbody = TableEl.querySelector("tbody");

			if (!TheadTr || !Tbody) {
				TL.DebugLog("Report-Table template is missing <thead><tr> or <tbody>");
				return TempDiv.innerHTML;
			}

			// Clear any placeholder content that may exist in the template
			TheadTr.innerHTML = "";
			Tbody.innerHTML = "";

			// Populate headers
			Headers.forEach((header) => {
				const th = document.createElement("th");
				th.className = "report-header-cell";
				th.textContent = header;
				TheadTr.appendChild(th);
			});

			// Populate rows
			Rows.forEach((rowArray) => {
				const tr = document.createElement("tr");
				tr.className = "report-row";
				rowArray.forEach((cellValue) => {
					const td = document.createElement("td");
					td.className = "report-cell";
					td.textContent = cellValue;
					tr.appendChild(td);
				});
				Tbody.appendChild(tr);
			});

			// Return the filled wrapper HTML to be injected into the Report-View
			return TempDiv.innerHTML;
		}

		/*
		 **
		 ** Render Report Table
		 ** ====================
		 ** Injects the generated table HTML into the single-report view.
		 */
		function RenderReportTable(TableHTML, ViewEl) {
			if (!ViewEl) return;
			try {
				// Common selectors we might find in the Report-View template
				const Selectors = [".report-table", ".report-table-container", ".report-table-wrapper", ".report-view-table", ".report-view-table-container", ".report-table-area"];

				let Container = null;
				for (let i = 0; i < Selectors.length; i++) {
					Container = ViewEl.querySelector(Selectors[i]);
					if (Container) break;
				}

				// If template used a loading placeholder, use its parent as container
				if (!Container) {
					const Loading = ViewEl.querySelector(".report-table-loading");
					if (Loading) Container = Loading.parentElement || Loading;
				}

				// Final fallback to the view element itself
				if (!Container) Container = ViewEl.querySelector(".report-view") || ViewEl;

				// If no HTML returned, show a friendly message instead of blank
				if (!TableHTML) {
					Container.innerHTML = '<div class="report-table-error">No report data available.</div>';
					return;
				}

				// Replace the container contents with the provided HTML
				Container.innerHTML = TableHTML;

				// Dispatch a small event so other scripts can initialize tables/plugins
				const TableEl = Container.querySelector("table");
				if (TableEl && typeof CustomEvent === "function") {
					TableEl.dispatchEvent(new CustomEvent("reporttable:loaded", { detail: { view: ViewEl } }));
				}
			} catch (err) {
				TL.DebugLog("ReplaceReportTable error:", err);
			}
		}

		/*
		 ** Initialize Report Table Search
		 ** ==============================
		 ** Wires the toolbar search input to filter visible rows in the rendered table.
		 */
		function InitializeReportTableSearch(ViewEl) {
			if (!ViewEl) return;
			const Wrapper = ViewEl.querySelector(".report-table-wrapper");
			if (!Wrapper) return;
			const SearchInput = Wrapper.querySelector(".rt-search-input");
			const Table = Wrapper.querySelector("table");
			if (!SearchInput || !Table) return;

			const TBody = Table.tBodies && Table.tBodies[0];
			if (!TBody) return;

			// Cache original row text for fast filtering without re-rendering
			const Rows = Array.from(TBody.rows);
			const Cache = Rows.map((tr) => ({ tr, text: tr.textContent.toLowerCase() }));

			// Helper: apply filter
			const applyFilter = (term) => {
				const q = (term || "").trim().toLowerCase();
				if (!q) {
					Cache.forEach(({ tr }) => (tr.style.display = ""));
					return;
				}
				for (let i = 0; i < Cache.length; i++) {
					Cache[i].tr.style.display = Cache[i].text.indexOf(q) !== -1 ? "" : "none";
				}
			};

			// Debounce input to reduce work while typing
			let DebounceTimer = null;
			const onInput = (e) => {
				const val = e.target.value;
				if (DebounceTimer) window.clearTimeout(DebounceTimer);
				DebounceTimer = window.setTimeout(() => applyFilter(val), 120);
			};

			SearchInput.addEventListener("input", onInput);

			// Recompute cache if table changes again later
			Table.addEventListener("reporttable:loaded", () => {
				const NewRows = Array.from(TBody.rows);
				Cache.length = 0;
				for (let i = 0; i < NewRows.length; i++) {
					Cache.push({ tr: NewRows[i], text: NewRows[i].textContent.toLowerCase() });
				}
				applyFilter(SearchInput.value);
			});
		}

		/**
		 * InitializeReportFilters
		 * --------------------------------
		 * Wires up the Report View table filtering UI. This includes:
		 * - Opening/closing the filter panel
		 * - Building per-column dynamic filters (text contains, select options, numeric ranges)
		 * - Providing an advanced rule builder (field + operator + value)
		 * - Applying all filters (including toolbar search) to show/hide table rows
		 *
		 * Notes:
		 * - This function performs DOM queries scoped to the provided view element to avoid leaking across views.
		 * - All internal identifiers use PascalCase for improved readability and consistency within this function.
		 *
		 * @param {HTMLElement} ViewElement - The root element of the single report view where the table and filter UI are rendered.
		 * @returns {void}
		 */
		function InitializeReportFilters(ViewElement) {
			if (!ViewElement) return;
			// Container that wraps the toolbar, table and filter UI for the report view
			const TableWrapperElement = ViewElement.querySelector(".report-table-wrapper");
			if (!TableWrapperElement) return;
			// UI controls
			const FilterToggleButton = TableWrapperElement.querySelector(".rt-filter-btn");
			const FilterPanelElement = TableWrapperElement.querySelector("#reportFilterPanel");
			const ResetFiltersButton = TableWrapperElement.querySelector(".rt-filter-reset");
			if (!FilterToggleButton || !FilterPanelElement) return;

			// Toggle the visibility of the filter panel
			FilterToggleButton.addEventListener("click", () => {
				const IsExpanded = FilterToggleButton.getAttribute("aria-expanded") === "true";
				FilterToggleButton.setAttribute("aria-expanded", String(!IsExpanded));
				FilterPanelElement.hidden = IsExpanded;
			});

			// Resolve table elements used for filtering
			const TableElement = TableWrapperElement.querySelector("table");
			const TableBodyElement = TableElement?.tBodies?.[0];
			if (!TableElement || !TableBodyElement) return;

			// Gather table headers and a helper to read cell text by index
			const TableHeaders = Array.from(TableElement.tHead?.rows?.[0]?.cells || []).map((HeaderCell) => HeaderCell.textContent.trim().toLowerCase());
			const GetCellTextForIndex = (RowElement, ColumnIndex) => (ColumnIndex >= 0 ? (RowElement.cells[ColumnIndex]?.textContent || "").trim() : RowElement.textContent);

			const DynamicFiltersContainer = TableWrapperElement.querySelector("#rtFilterDynamic");

			// Function to capitalize the first letter of a string
			function Capitalize(Text = "") {
				try {
					return Text.charAt(0).toUpperCase() + Text.slice(1);
				} catch (e) {
					return Text;
				}
			}
			function BuildDynamicFilters() {
				if (!DynamicFiltersContainer) return;
				// Preserve current values for dynamic inputs so they persist across rebuilds
				const PreviousValues = {};
				DynamicFiltersContainer.querySelectorAll("[data-colkey]").forEach((Element) => {
					PreviousValues[Element.dataset.colkey] = Element.value;
				});
				DynamicFiltersContainer.innerHTML = "";
				const Descriptors = [];
				TableHeaders.forEach((HeaderKey, ColumnIndex) => {
					if (!HeaderKey) return;
					const Rows = Array.from(TableBodyElement.rows);
					const Values = [];
					const SampleCount = Math.min(Rows.length, 500);
					for (let r = 0; r < SampleCount; r++) {
						const Text = (Rows[r].cells[ColumnIndex]?.textContent || "").trim();
						if (Text) Values.push(Text);
					}
					const UniqueValues = Array.from(new Set(Values));
					const NumericValues = Values.filter((V) => !isNaN(parseFloat(V)) && isFinite(parseFloat(V)));
					const MajorityNumeric = Values.length > 0 && NumericValues.length / Values.length >= 0.9;

					if (MajorityNumeric) {
						const RowEl = document.createElement("div");
						RowEl.className = "rt-filter-row";
						const LabelEl = document.createElement("label");
						LabelEl.textContent = Capitalize(HeaderKey);
						const MinInput = document.createElement("input");
						MinInput.type = "number";
						MinInput.className = "rt-input";
						MinInput.placeholder = "min";
						MinInput.dataset.colkey = HeaderKey + "__min";
						MinInput.dataset.colindex = ColumnIndex;
						const SeparatorEl = document.createElement("span");
						SeparatorEl.className = "rt-sep";
						SeparatorEl.textContent = "–";
						const MaxInput = document.createElement("input");
						MaxInput.type = "number";
						MaxInput.className = "rt-input";
						MaxInput.placeholder = "max";
						MaxInput.dataset.colkey = HeaderKey + "__max";
						MaxInput.dataset.colindex = ColumnIndex;
						if (PreviousValues[MinInput.dataset.colkey]) MinInput.value = PreviousValues[MinInput.dataset.colkey];
						if (PreviousValues[MaxInput.dataset.colkey]) MaxInput.value = PreviousValues[MaxInput.dataset.colkey];
						RowEl.append(LabelEl, MinInput, SeparatorEl, MaxInput);
						DynamicFiltersContainer.appendChild(RowEl);
						Descriptors.push({ type: "number-range", index: ColumnIndex, get: () => ({ min: parseFloat(MinInput.value), max: parseFloat(MaxInput.value) }) });
					} else if (UniqueValues.length > 0 && UniqueValues.length <= 12) {
						UniqueValues.sort();
						const RowEl = document.createElement("div");
						RowEl.className = "rt-filter-row";
						const LabelEl = document.createElement("label");
						LabelEl.textContent = Capitalize(HeaderKey);
						const SelectEl = document.createElement("select");
						SelectEl.className = "rt-select-input";
						SelectEl.dataset.colkey = HeaderKey;
						SelectEl.dataset.colindex = ColumnIndex;
						const AnyOption = document.createElement("option");
						AnyOption.value = "";
						AnyOption.textContent = "Any";
						SelectEl.appendChild(AnyOption);
						UniqueValues.forEach((V) => {
							const OptionEl = document.createElement("option");
							OptionEl.value = V;
							OptionEl.textContent = V;
							SelectEl.appendChild(OptionEl);
						});
						if (PreviousValues[HeaderKey]) SelectEl.value = PreviousValues[HeaderKey];
						RowEl.append(LabelEl, SelectEl);
						DynamicFiltersContainer.appendChild(RowEl);
						Descriptors.push({ type: "select", index: ColumnIndex, get: () => SelectEl.value });
					} else {
						const RowEl = document.createElement("div");
						RowEl.className = "rt-filter-row";
						const LabelEl = document.createElement("label");
						LabelEl.textContent = Capitalize(HeaderKey);
						const InputEl = document.createElement("input");
						InputEl.type = "text";
						InputEl.className = "rt-input";
						InputEl.placeholder = "contains…";
						InputEl.dataset.colkey = HeaderKey;
						InputEl.dataset.colindex = ColumnIndex;
						if (PreviousValues[HeaderKey]) InputEl.value = PreviousValues[HeaderKey];
						RowEl.append(LabelEl, InputEl);
						DynamicFiltersContainer.appendChild(RowEl);
						Descriptors.push({ type: "text", index: ColumnIndex, get: () => InputEl.value });
					}
				});
				FilterPanelElement._descriptors = Descriptors;
			}

			// --- Dynamic Filter Builder (Field + Operator + Value) ---

			const FieldSelectElement = TableWrapperElement.querySelector("#rtFieldSelect");
			const OperatorSelectElement = TableWrapperElement.querySelector("#rtOperatorSelect");
			const ValueInputWrapper = TableWrapperElement.querySelector("#rtValueInputWrap");
			const AddFilterButton = TableWrapperElement.querySelector(".rt-add-filter");
			const ActiveFiltersContainer = TableWrapperElement.querySelector("#rtActiveFilters");
			const ClearFiltersButton = TableWrapperElement.querySelector(".rt-clear-btn");

			FilterPanelElement._activeFilters = FilterPanelElement._activeFilters || [];

			// Determine column types by sampling values
			const ColumnsMetadata = TableHeaders.map((HeaderKey, ColumnIndex) => {
				const Rows = Array.from(TableBodyElement.rows);
				const SampleCount = Math.min(Rows.length, 500);
				let NumericCount = 0,
					NonEmptyCount = 0;
				for (let r = 0; r < SampleCount; r++) {
					const Text = (Rows[r].cells[ColumnIndex]?.textContent || "").trim();
					if (!Text) continue;
					NonEmptyCount++;
					if (!isNaN(parseFloat(Text)) && isFinite(parseFloat(Text))) NumericCount++;
				}
				let InferredType = "text";
				if (NonEmptyCount > 0) {
					if (NumericCount / NonEmptyCount >= 0.9) InferredType = "number";
				}
				return { index: ColumnIndex, key: HeaderKey, type: InferredType };
			});

			const OperatorsMap = {
				text: [
					{ id: "contains", label: "contains" },
					{ id: "equals", label: "equals" },
					{ id: "starts", label: "starts with" },
					{ id: "ends", label: "ends with" },
					{ id: "not_contains", label: "does not contain" },
					{ id: "not_equals", label: "does not equal" },
				],
				number: [
					{ id: "eq", label: "=" },
					{ id: "ne", label: "≠" },
					{ id: "gt", label: ">" },
					{ id: "gte", label: ">=" },
					{ id: "lt", label: "<" },
					{ id: "lte", label: "<=" },
					{ id: "between", label: "between" },
				],
			};

			function FillFieldOptions() {
				if (!FieldSelectElement) return;
				FieldSelectElement.innerHTML = "";
				const AnyOption = document.createElement("option");
				AnyOption.value = "";
				AnyOption.textContent = "Select field";
				FieldSelectElement.appendChild(AnyOption);
				ColumnsMetadata.forEach((ColumnMeta) => {
					const OptionEl = document.createElement("option");
					OptionEl.value = String(ColumnMeta.index);
					OptionEl.textContent = Capitalize(ColumnMeta.key);
					OptionEl.dataset.type = ColumnMeta.type;
					FieldSelectElement.appendChild(OptionEl);
				});
			}

			function FillOperatorOptions(Type) {
				if (!OperatorSelectElement) return;
				OperatorSelectElement.innerHTML = "";
				const OperatorsList = OperatorsMap[Type || "text"] || OperatorsMap.text;
				const AnyOption = document.createElement("option");
				AnyOption.value = "";
				AnyOption.textContent = "Select";
				OperatorSelectElement.appendChild(AnyOption);
				OperatorsList.forEach((Operator) => {
					const OptionEl = document.createElement("option");
					OptionEl.value = Operator.id;
					OptionEl.textContent = Operator.label;
					OperatorSelectElement.appendChild(OptionEl);
				});
			}

			function RenderValueInput(Type, Operator) {
				if (!ValueInputWrapper) return;
				ValueInputWrapper.innerHTML = "";
				const MakeInput = (InputType, Id) => {
					const InputEl = document.createElement("input");
					InputEl.type = InputType;
					InputEl.className = "rt-input";
					InputEl.placeholder = "value";
					if (Id) InputEl.id = Id;
					return InputEl;
				};
				if (Operator === "between") {
					const ValueAInput = MakeInput(Type === "number" ? "number" : "text", "rtValA");
					const SeparatorEl = document.createElement("span");
					SeparatorEl.className = "rt-sep";
					SeparatorEl.textContent = "–";
					const ValueBInput = MakeInput(Type === "number" ? "number" : "text", "rtValB");
					ValueInputWrapper.append(ValueAInput, SeparatorEl, ValueBInput);
					ValueInputWrapper._getValue = () => ({ a: ValueAInput.value, b: ValueBInput.value });
				} else {
					const ValueAInput = MakeInput(Type === "number" ? "number" : "text", "rtValA");
					ValueInputWrapper.append(ValueAInput);
					ValueInputWrapper._getValue = () => ({ a: ValueAInput.value });
				}
			}

			function OnFieldChange() {
				const SelectedOption = FieldSelectElement?.selectedOptions?.[0];
				const Type = SelectedOption?.dataset?.type || "text";
				FillOperatorOptions(Type);
				RenderValueInput(Type, "");
			}

			function OnOperatorChange() {
				const SelectedOption = FieldSelectElement?.selectedOptions?.[0];
				const Type = SelectedOption?.dataset?.type || "text";
				RenderValueInput(Type, OperatorSelectElement?.value || "");
			}

			function RenderActiveFilters() {
				if (!ActiveFiltersContainer) return;
				ActiveFiltersContainer.innerHTML = "";
				FilterPanelElement._activeFilters.forEach((Filter, Index) => {
					const ChipEl = document.createElement("div");
					ChipEl.className = "rt-chip";
					const LabelEl = document.createElement("span");
					LabelEl.className = "rt-chip-label";
					const ValueText = Filter.op === "between" ? `${Filter.valA} – ${Filter.valB}` : Filter.valA;
					LabelEl.textContent = `${Capitalize(Filter.key)} ${Filter.label} ${ValueText}`;
					const RemoveButton = document.createElement("button");
					RemoveButton.type = "button";
					RemoveButton.className = "rt-chip-remove";
					RemoveButton.textContent = "×";
					RemoveButton.setAttribute("aria-label", `Remove filter ${Filter.key}`);
					RemoveButton.addEventListener("click", () => {
						FilterPanelElement._activeFilters.splice(Index, 1);
						RenderActiveFilters();
						ApplyFilters();
					});
					ChipEl.append(LabelEl, RemoveButton);
					ActiveFiltersContainer.appendChild(ChipEl);
				});
			}

			function AddCurrentFilter() {
				const FieldOptionEl = FieldSelectElement?.selectedOptions?.[0];
				const OperatorId = OperatorSelectElement?.value || "";
				if (!FieldOptionEl || !FieldOptionEl.value || !OperatorId || !ValueInputWrapper || !ValueInputWrapper._getValue) return;
				const ColumnIndex = parseInt(FieldOptionEl.value, 10);
				const Meta = ColumnsMetadata.find((C) => C.index === ColumnIndex) || { type: "text" };
				const Value = ValueInputWrapper._getValue();
				if (!Value || (!Value.a && OperatorId !== "between")) return;
				const Rule = {
					index: ColumnIndex,
					key: FieldOptionEl.textContent || Meta.key,
					type: Meta.type,
					op: OperatorId,
					valA: (Value.a || "").trim(),
					valB: (Value.b || "").trim(),
					label: (OperatorsMap[Meta.type] || OperatorsMap.text).find((O) => O.id === OperatorId)?.label || OperatorId,
				};
				FilterPanelElement._activeFilters.push(Rule);
				RenderActiveFilters();
				ApplyFilters();
			}

			FieldSelectElement?.addEventListener("change", OnFieldChange);
			OperatorSelectElement?.addEventListener("change", OnOperatorChange);
			AddFilterButton?.addEventListener("click", AddCurrentFilter);

			ClearFiltersButton?.addEventListener("click", () => {
				const SearchInput = TableWrapperElement.querySelector(".rt-search-input");
				if (SearchInput) SearchInput.value = "";
				FilterPanelElement._activeFilters = [];
				RenderActiveFilters();
				ApplyFilters();
			});

			FillFieldOptions();
			OnFieldChange();

			function ApplyFilters() {
				const SearchQuery = (TableWrapperElement.querySelector(".rt-search-input")?.value || "").toLowerCase();

				const ActiveRules = Array.isArray(FilterPanelElement._activeFilters) ? FilterPanelElement._activeFilters : [];
				const EvaluateRule = (Rule, CellText) => {
					const Text = (CellText || "").trim();
					if (Rule.type === "number") {
						const N = parseFloat(Text);
						if (!Number.isFinite(N)) return false;
						const A = parseFloat(Rule.valA);
						const B = parseFloat(Rule.valB);
						if (Rule.op === "eq") return N === A;
						if (Rule.op === "ne") return N !== A;
						if (Rule.op === "gt") return N > A;
						if (Rule.op === "gte") return N >= A;
						if (Rule.op === "lt") return N < A;
						if (Rule.op === "lte") return N <= A;
						if (Rule.op === "between") return (Number.isFinite(A) ? N >= A : true) && (Number.isFinite(B) ? N <= B : true);
						return true;
					}
					// text
					const Lower = Text.toLowerCase();
					const AText = String(Rule.valA || "").toLowerCase();
					if (Rule.op === "contains") return Lower.includes(AText);
					if (Rule.op === "equals") return Lower === AText;
					if (Rule.op === "starts") return Lower.startsWith(AText);
					if (Rule.op === "ends") return Lower.endsWith(AText);
					if (Rule.op === "not_contains") return !Lower.includes(AText);
					if (Rule.op === "not_equals") return Lower !== AText;
					return true;
				};

				Array.from(TableBodyElement.rows).forEach((RowElement) => {
					const RowText = RowElement.textContent.toLowerCase();
					const MatchesSearchQuery = !SearchQuery || RowText.includes(SearchQuery);

					let MatchesColumnFilters = true;
					if (FilterPanelElement && Array.isArray(FilterPanelElement._descriptors)) {
						for (const Descriptor of FilterPanelElement._descriptors) {
							const CellText = GetCellTextForIndex(RowElement, Descriptor.index);
							if (Descriptor.type === "select") {
								const Value = (Descriptor.get() || "").trim().toLowerCase();
								if (Value && CellText.trim().toLowerCase() !== Value) {
									MatchesColumnFilters = false;
									break;
								}
							} else if (Descriptor.type === "text") {
								const Value = (Descriptor.get() || "").trim().toLowerCase();
								if (Value && !CellText.toLowerCase().includes(Value)) {
									MatchesColumnFilters = false;
									break;
								}
							} else if (Descriptor.type === "number-range") {
								const Range = Descriptor.get();
								const N = parseFloat(CellText);
								if ((Number.isFinite(Range.min) && !(N >= Range.min)) || (Number.isFinite(Range.max) && !(N <= Range.max))) {
									MatchesColumnFilters = false;
									break;
								}
							}
						}
					}

					// Evaluate advanced builder rules (AND)
					let MatchesAdvancedRules = true;
					if (ActiveRules.length) {
						for (const Rule of ActiveRules) {
							const CellText = GetCellTextForIndex(RowElement, Rule.index);
							if (!EvaluateRule(Rule, CellText)) {
								MatchesAdvancedRules = false;
								break;
							}
						}
					}

					RowElement.style.display = MatchesSearchQuery && MatchesColumnFilters && MatchesAdvancedRules ? "" : "none";
				});
			}

			// Build dynamic filters initially
			BuildDynamicFilters();

			ResetFiltersButton?.addEventListener("click", () => {
				if (DynamicFiltersContainer) DynamicFiltersContainer.querySelectorAll("input, select").forEach((El) => (El.value = ""));
				ApplyFilters();
			});

			// Rebuild and apply when table re-renders
			TableElement.addEventListener("reporttable:loaded", () => {
				BuildDynamicFilters();
				ApplyFilters();
			});
		}

		/*
		 **
		 ** Show Empty State
		 ** ===============
		 */
		function ShowEmptyState() {
			if (Elements.ReportsGrid) HideElement(Elements.ReportsGrid);
			if (Elements.EmptyState) ShowElement(Elements.EmptyState);
		}

		/*
		 **
		 ** Generate Report
		 ** ==============
		 */
		function GenerateReport(ReportName, ReportInfo) {
			TL.DebugLog("Generate report requested:", ReportName, ReportInfo);

			// Check if user is already downloading a report
			const MainWrapper = document.querySelector("main") || document.body;
			if (MainWrapper.querySelector && MainWrapper.querySelector(".TL-Loading")) {
				TL.Notify.Banner("Please wait", "Please wait for the current report to finish downloading");
				return false;
			}

			// Start the download process
			DownloadReport(ReportName, ReportInfo);
		}

		/*
		 **
		 ** Toggle Favorite Status
		 ** =====================
		 */
		function ToggleFavorite(FavoriteBtn, Report) {
			const ReportId = Report.ID;
			const IsFavorited = State.FavoriteReports.includes(ReportId);

			if (IsFavorited) {
				// Removing from favorites: call server-side RemoveFavorite via agent
				try {
					if (FavoriteBtn) FavoriteBtn.disabled = true;
					RemoveFavorite(ReportId)
						.then((Result) => {
							// Remove from local state on success
							State.FavoriteReports = State.FavoriteReports.filter((Id) => Id !== ReportId);
							if (FavoriteBtn) {
								FavoriteBtn.classList.remove("favorited");
								FavoriteBtn.title = "Add to favorites";
							}
							TL.DebugLog("Removed from favorites:", Report.Name, Result);
							// Update UI
							UpdateFavoritesData();
							UpdateFavoritesCount();
							if (State.CurrentCategory === "favorites") {
								LoadCategory("favorites");
							}
						})
						.catch((err) => {
							TL.Notify.Banner("Error", err || "Failed to remove favorite");
							TL.DebugLog("RemoveFavorite failed:", err);
						})
						.finally(() => {
							if (FavoriteBtn) FavoriteBtn.disabled = false;
						});
				} catch (err) {
					TL.DebugLog("ToggleFavorite remove error:", err);
				}

				return;
			}

			// Adding to favorites: call server-side AddFavorite via agent
			try {
				if (FavoriteBtn) FavoriteBtn.disabled = true;
				AddFavorite(ReportId)
					.then((Result) => {
						// Only add if not already present (avoid duplicates)
						if (!State.FavoriteReports.includes(ReportId)) {
							// Put newly added favorites at the front so they appear first in the UI
							State.FavoriteReports.unshift(ReportId);
						}
						FavoriteBtn.classList.add("favorited");
						FavoriteBtn.title = "Remove from favorites";
						TL.DebugLog("Added to favorites:", Report.Name, Result);
						// Update UI
						UpdateFavoritesData();
						UpdateFavoritesCount();
						if (State.CurrentCategory === "favorites") {
							LoadCategory("favorites");
						}
					})
					.catch((err) => {
						TL.Notify.Banner("Error", err || "Failed to add favorite");
						TL.DebugLog("AddFavorite failed:", err);
					})
					.finally(() => {
						if (FavoriteBtn) FavoriteBtn.disabled = false;
					});
			} catch (err) {
				TL.DebugLog("ToggleFavorite error:", err);
			}
		}

		/*
		 **
		 ** Update Favorites Data
		 ** ====================
		 */
		function UpdateFavoritesData() {
			const FavoriteReports = [];

			// Build a map of reports by ID for quick lookup across all categories
			const ReportById = {};
			Object.keys(State.ReportData).forEach((CatKey) => {
				const Cat = State.ReportData[CatKey];
				if (!Cat || !Array.isArray(Cat.Reports)) return;
				Cat.Reports.forEach((R) => {
					if (R && R.ID !== undefined) ReportById[R.ID] = { ...R, OriginalCategory: CatKey };
				});
			});

			// Map favorite IDs to report objects, preserving order and deduping
			const Seen = new Set();
			State.FavoriteReports.forEach((FavId) => {
				const Rep = ReportById[FavId];
				if (Rep && !Seen.has(FavId)) {
					// Push to preserve the server-provided order (ServerFavorites are ordered by created_at DESC)
					FavoriteReports.push(Rep);
					Seen.add(FavId);
				}
			});

			// Update The Favorites Category Data
			State.ReportData.favorites.Reports = FavoriteReports;
		}

		/*
		 **
		 ** Update Favorites Count Display
		 ** =============================
		 */
		function UpdateFavoritesCount() {
			if (Elements.FavoritesCount) {
				const Count = State.FavoriteReports.length;
				Elements.FavoritesCount.textContent = `${Count} Report${Count === 1 ? "" : "s"}`;
			}
		}

		/*
		 **
		 ** Get Reports
		 ** =====================
		 */
		function GetReports() {
			return new Promise((resolve, reject) => {
				TL.Agent({
					agent: ["app", "get-user-reports"],
					data: {},
					success(Result) {
						TL.DebugLog("Fetched Reports:", Result);

						resolve(Result);
					},
					error(error) {
						TL.Notify.Banner("Error", error);
						reject(error);
					},
				});
			});
		}

		/*
		 **
		 ** Get Report Categories
		 ** ====================
		 */
		function GetReportCategories() {
			return new Promise((resolve, reject) => {
				TL.Agent({
					agent: ["app", "get-report-categories"],
					data: {},
					success(Result) {
						resolve(Result || {});
					},
					error(error) {
						TL.DebugLog("GetReportCategories error:", error);
						reject(error);
					},
				});
			});
		}

		/*
		 **
		 ** Export Report Using Downloader
		 ** ==============================
		 */
		async function ExportReport(Data, ReportInfo) {
			try {
				// Use the Downloader to export the report with the correct file type and display name
				return await TL.Downloader.Export({
					Data,
					FileType: ReportInfo.Format || "csv",
					DisplayName: ReportInfo.DisplayName || ReportInfo.Name,
					ConfigurationName: ReportInfo.Name,
				});
			} catch (error) {
				TL.DebugLog("Export error:", error);
				TL.Notify.Banner("Export Error", "Failed to export the report");
			}
		}

		/*
		 **
		 ** Retrieve and Download Report
		 ** ============================
		 */
		async function DownloadReport(ReportName, ReportInfo) {
			// Start loading
			Loader.Start();

			try {
				// Get the report data using the same agent as FetchReportTableData
				const Result = await new Promise((resolve, reject) => {
					TL.Agent({
						agent: ["app", "retrieve"],
						data: {
							ReportName: ReportName,
						},
						success(Result) {
							TL.DebugLog("Report retrieved for download:", Result);
							resolve(Result);
						},
						error(error) {
							TL.DebugLog("Download report retrieve failed:", error);
							reject(error);
						},
					});
				});

				TL.DebugLog("Report data retrieved for download:", Result);

				// Check if we have valid data to export
				if (Array.isArray(Result) && Result.length > 0) {
					// Export the real report data using the downloader
					await ExportReport(Result, ReportInfo);
					TL.DebugLog("Report exported successfully:", ReportName);
				} else {
					// Handle case where no data was returned
					TL.Notify.Banner("No Data", "No data available for this report");
					TL.DebugLog("No data to export for report:", ReportName);
				}
			} catch (error) {
				TL.Notify.Banner("Error", error || "Failed to retrieve report data");
				TL.DebugLog("DownloadReport error:", error);
			} finally {
				Loader.Stop();
			}
		}
	});

	/*
	 **
	 ** Get Favorites
	 ** ============
	 */
	function GetFavorites() {
		return new Promise((resolve, reject) => {
			TL.Agent({
				agent: ["app", "get-favorites"],
				data: {},
				success(Result) {
					TL.DebugLog("Favorites retrieved:", Result);
					resolve(Result);
				},
				error(error) {
					reject(error);
				},
			});
		});
	}

	/*
	 **
	 ** Add Favorite
	 ** ============
	 */
	function AddFavorite(ReportId) {
		return new Promise((resolve, reject) => {
			TL.Agent({
				agent: ["app", "add-favorite"],
				data: {
					ReportId: ReportId,
				},
				success(Result) {
					TL.DebugLog("Favorite added:", Result);
					resolve(Result);
				},
				error(error) {
					reject(error);
				},
			});
		});
	}

	/*
	 **
	 ** Remove Favorite
	 ** ===============
	 */
	function RemoveFavorite(ReportId) {
		return new Promise((resolve, reject) => {
			TL.Agent({
				agent: ["app", "remove-favorite"],
				data: {
					ReportId: ReportId,
				},
				success(Result) {
					TL.DebugLog("Favorite removed:", Result);
					resolve(Result);
				},
				error(error) {
					reject(error);
				},
			});
		});
	}
});
