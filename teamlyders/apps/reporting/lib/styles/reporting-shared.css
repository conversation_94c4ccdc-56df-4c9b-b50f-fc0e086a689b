/* ========================================================================
	reporting-shared.css - Shared Reporting App Styles
	======================================================================== */

/* ------------------------------------------------------------------------
	1. Layout & Containers
	------------------------------------------------------------------------ */

/* Main App Container */
.reporting-app {
	display: flex;
	min-height: 100vh;
	background: #fff;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Main Content Area */
.main-content {
	flex: 1;
	padding: 24px 32px;
	overflow-y: auto;
}

/* Grid Layout for Reports */
.reports-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
	gap: 24px;
}

/* Grid Layout for Relevant Categories */
.relevant-categories-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
	gap: 24px;
	margin-top: 8px;
}

/* Utility: Hidden helper to hide elements (used to replace inline display:none) */
.hidden {
	display: none !important;
}

/* ------------------------------------------------------------------------
   Charts: shared layout and visual styles
   ------------------------------------------------------------------------ */

/* Charts section wrapper */
.reporting-charts-section {
	margin-top: 8px;
}

/* Charts responsive grid */
.reporting-charts-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
	gap: 24px;
}

/* Chart card container (reuses .card base styles) */
.chart-card {
	position: relative;
}

/* Chart title */
.chart-title {
	font-size: 16px;
	font-weight: 600;
	color: #1a202c;
	margin-bottom: 24px;
}

/* Generic chart container area for TL.Charts to mount into */
.chart-container {
	width: 100%;
	background: transparent;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* Dark mode tweaks for charts */
.reporting-app.dark .chart-title {
	color: #e5e7eb;
	margin-bottom: 12px;
}

.reporting-app.dark .chart-container {
	background: transparent;
}

.reporting-app.dark .chart-container .bb-axis,
.reporting-app.dark .chart-container .bb-axis line,
.reporting-app.dark .chart-container .bb-axis path {
	stroke: #e5e7eb;
}

/* ------------------------------------------------------------------------
   2. Header & Section Styles
   ------------------------------------------------------------------------ */

/* Content Header */
.content-header {
	margin-bottom: 24px;
}

.content-header h2 {
	font-size: 28px;
	margin: 0 0 8px 0;
	color: #1a202c;
	font-weight: 600;
}

.content-header p {
	color: #717596;
	margin: 0;
	font-size: 14px;
}

/* Section Header */
.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.section-header h2 {
	font-size: 24px;
	margin: 0;
	color: #1a202c;
	font-weight: 600;
}

.section-description {
	color: #717596;
	margin: 0 0 20px 0;
	font-size: 14px;
}

/* ------------------------------------------------------------------------
   3. Buttons
   ------------------------------------------------------------------------ */

/* Primary Button */
.btn-primary {
	background: #6366f1;
	color: white;
	border: none;
	padding: 12px 24px;
	border-radius: 10px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.2s ease;
}

.btn-primary:hover {
	background: #5855eb;
}

.btn-primary:active {
	animation: btnBounce 0.1s ease-in-out;
}

/* Secondary Button */
.btn-secondary {
	color: #525252;
	border: 1px solid #dee2e6;
	padding: 12px 24px;
	border-radius: 10px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.2s ease;
}

.btn-secondary:hover {
	background: #f5f5f5;
	border-color: #d1d5db;
}

.btn-secondary:active {
	animation: btnBounce 0.1s ease-in-out;
}

/* View All Button */
.view-all-btn {
	padding: 8px 16px;
}

/* Generate Button */
.generate-btn {
	background: #6366f1;
	color: white;
	border: none;
	padding: 8px 16px;
	border-radius: 8px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.2s ease;
	flex: 1;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	min-height: 35px; /* ensures consistent height with view button */
}

.generate-btn:hover {
	background: #5855eb;
}

.generate-btn:active {
	animation: btnBounce 0.1s ease-in-out;
}

/* View button — same size as generate-btn but visually secondary */
.view-btn {
	padding: 8px 16px;
	flex: 0 0 auto; /* keep independent width but match height */
	display: inline-flex;
	align-items: center;
	justify-content: center;
	min-height: 36px; /* match generate button height */
}

/* Favorite Button */
.favorite-btn {
	background: #f8f9fa;
	border: 1px solid #dee2e6;
	color: #6c757d;
	border-radius: 8px;
	padding: 8px;
	cursor: pointer;
	transition: all 0.2s ease;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	flex: 0 0 auto; /* keep independent width */
	min-height: 36px; /* match generate/view buttons */
}

.favorite-btn svg {
	width: 16px;
	height: 16px;
	fill: none;
	stroke: currentColor;
	stroke-width: 2;
	transition: all 0.2s ease;
}

.favorite-btn:hover {
	background: #f5f5f5;
	border-color: #d1d5db;
}

.favorite-btn.favorited svg {
	fill: #dc3545;
	stroke: #dc3545;
}

.favorite-btn:active {
	animation: btnBounce 0.1s ease-in-out;
}

/* ------------------------------------------------------------------------
   4. Card & Report Card Styles
   ------------------------------------------------------------------------ */

/* Shared Card Styles */
.card {
	background: #fff;
	border: 1px solid #e2e8f0;
	border-radius: 24px;
	padding: 24px;
	transition: all 0.3s ease;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.card:hover {
	border-color: #d1d5db;
}

/* Report Card */
.report-card {
	/* Uses shared .card styles */
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
}

.report-card-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 16px;
}

.report-icon {
	width: 48px;
	height: 48px;
	border-radius: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16px;
	font-size: 20px;
	flex-shrink: 0;
	background: #f7f8fa;
	color: #495057;
	border: 1px solid #dee2e6;
}

.report-content {
	flex: 1;
}

.report-content h3 {
	font-size: 18px;
	margin: 0 0 8px 0;
	color: #1a202c;
	font-weight: 600;
	line-height: 1.3;
}

.report-content p {
	margin: 0 0 16px 0;
	color: #64748b;
	font-size: 14px;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

.report-content p.no-description {
	color: #94a3b8;
	font-style: italic;
}

.report-card-footer {
	margin-top: auto;
	padding-top: 4px;
	display: flex;
	gap: 8px;
	align-items: center;
}

/* ------------------------------------------------------------------------
   4b. Single Report View
   ------------------------------------------------------------------------ */
/* Base container */
.report-view {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	animation: fadeIn 0.25s ease;
}

/* 1. Header */
.report-view-header {
	display: flex;
	flex-direction: column;
	gap: 24px;
	margin-bottom: 32px;
}
.report-view-bar {
	display: flex;
	justify-content: flex-start; /* left group natural width */
	align-items: center;
	flex-wrap: wrap;
	gap: 12px;
}
.rv-left {
	display: flex;
	align-items: center;
	gap: 16px;
}
/* Actions cluster */
.report-view-header .rv-actions {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-left: auto; /* push cluster to right */
}
.report-view-header .rv-actions > * {
	flex: 0 0 auto;
}
/* tighten secondary buttons, emphasize primary */
.report-view-header .rv-actions .refresh-btn {
	padding: 8px 14px;
}
.report-view-header .rv-actions .favorite-btn {
	width: 40px;
	min-width: 40px;
	height: 36px;
}
.report-view-header .rv-actions .generate-btn {
	padding: 8px 20px;
	font-weight: 600;
	position: relative;
}
.report-view-header .rv-actions .generate-btn::before {
	content: "";
	position: absolute;
	left: -14px;
	top: 4px;
	bottom: 4px;
	width: 1px;
	background: #e2e8f0;
}
.reporting-app.dark .report-view-header .rv-actions .generate-btn::before {
	background: #333;
}

/* Optional back button (lightweight style) */
.report-view-header .back-btn {
	padding: 8px 14px;
	display: inline-flex;
	align-items: center;
	gap: 6px;
}
.report-view-header .back-btn:hover {
	background: #f1f5f9;
}
.report-view-header .back-btn:active {
	animation: btnBounce 0.1s ease-in-out;
}

/* 3. Meta / Title */
.report-view-meta {
	display: flex;
	gap: 20px;
	align-items: flex-start;
}
.report-view-meta .report-icon.single-view-icon {
	width: 60px;
	height: 60px;
	border-radius: 16px;
	font-size: 30px;
	margin: 0;
	display: flex;
	align-items: center;
	justify-content: center;
}
.rv-title {
	font-size: 26px;
	margin: 0 0 6px 0;
	font-weight: 600;
	line-height: 32px;
	color: #1f2937;
}
.rv-subtitle {
	font-size: 14px;
	margin: 0 0 12px 0;
	color: #64748b;
	line-height: 20px;
	max-width: 900px;
}

/* 4. Body + Summary (summary currently optional) */
.report-view-body {
	display: flex;
	gap: 32px;
	align-items: flex-start;
}

.report-data-area {
	flex: 1 1 0%;
	min-width: 0; /* allow flex child to shrink below its contents when needed */
}
.report-table-wrapper {
	padding: 0; /* remove inner padding so table can span edge-to-edge inside the card */
	width: 100%;
}
.report-data-table {
	width: 100%;
	min-width: 0; /* override the previous min-width to allow full-width behavior */
}
.report-summary {
	width: 250px;
	display: flex;
	flex-direction: column;
	gap: 14px;
}
.summary-card {
	padding: 16px 18px;
	border-radius: 18px;
}
.summary-label {
	font-size: 11px;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	color: #64748b;
	font-weight: 600;
	margin-bottom: 4px;
}
.summary-value {
	font-size: 15px;
	font-weight: 600;
	color: #1f2937;
	word-break: break-word;
}

/* 5. Data Section */
.report-data-card {
	padding: 0;
	display: flex;
	flex-direction: column;
}
.report-data-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 22px 10px 22px;
}
.report-data-title {
	margin: 0;
	font-size: 20px;
	font-weight: 600;
	color: #1f2937;
}
.report-format-tag {
	font-size: 12px;
	font-weight: 600;
	background: #eef2ff;
	color: #4338ca;
	padding: 6px 10px;
	border-radius: 999px;
}
.report-data-card .report-table-wrapper {
	padding: 0 22px 22px;
}

/* ------------------------------------------------------------------------
   5a. Report Toolbar: search + filters (above table)
   ------------------------------------------------------------------------ */
.report-toolbar {
	/* sits above the scrollable table */
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 12px;
	padding: 10px 0 12px 0;
	border-bottom: 1px solid #e6edf3;
	margin: 0 0 10px 0;
}
.report-toolbar .rt-left,
.report-toolbar .rt-right {
	display: flex;
	align-items: center;
	gap: 12px;
	flex-wrap: wrap;
}

/* Search */
.rt-search {
	position: relative;
	display: inline-flex;
	align-items: center;
}
.rt-search-icon {
	position: absolute;
	left: 10px;
	color: #94a3b8;
}
.rt-search-input {
	height: 36px;
	padding: 8px 12px 8px 32px;
	border: 1px solid #e2e8f0;
	border-radius: 10px;
	background: #fff;
	color: #1f2937;
	outline: none;
	min-width: 240px;
	width: clamp(220px, 28vw, 360px);
	font-size: 14px;
}
.rt-search-input::placeholder {
	color: #94a3b8;
}
.rt-search-input:focus {
	border-color: #6366f1;
	box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
}

/* Hide the built-in clear (x) button on WebKit/Chromium browsers */
.rt-search-input::-webkit-search-cancel-button {
	-webkit-appearance: none;
	appearance: none;
	display: none;
}
/* Hide legacy MS clear button (no-op on modern browsers) */
.rt-search-input::-ms-clear {
	display: none;
	width: 0;
	height: 0;
}

/* Filters cluster */
.rt-filters {
	display: inline-flex;
	align-items: center;
	gap: 10px;
}
.rt-filter-btn {
	height: 36px;
	display: inline-flex;
	align-items: center;
	gap: 8px;
}
.rt-filter-icon {
	color: currentColor;
}

/* Selects + Clear */
.rt-select {
	display: inline-flex;
	align-items: center;
	gap: 8px;
}
.rt-select-label {
	font-size: 12px;
	color: #64748b;
	font-weight: 600;
}
.rt-select-input {
	height: 36px;
	border: 1px solid #e2e8f0;
	background: #fff;
	color: #1f2937;
	border-radius: 10px;
	padding: 6px 10px;
	outline: none;
}
.rt-select-input:focus {
	border-color: #6366f1;
	box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
}
.rt-clear-btn {
	height: 36px;
}

/* Dark mode */
.reporting-app.dark .report-toolbar {
	border-bottom-color: #333;
}
.reporting-app.dark .rt-search-icon {
	color: #a3a3a3;
}
.reporting-app.dark .rt-search-input {
	background: #1f1f1f;
	border-color: #404040;
	color: #f5f5f5;
}
.reporting-app.dark .rt-search-input::placeholder {
	color: #737373;
}
.reporting-app.dark .rt-divider {
	background: #333;
}
/* Advanced Filter Panel (date range) */
.rt-filter-panel {
	margin: 8px 0 12px;
	padding: 12px;
	border: 1px solid #e6edf3;
	border-radius: 12px;
	background: #fff;
}
.rt-filter-form {
	display: flex;
	flex-wrap: wrap;
	gap: 12px;
	align-items: center;
}
.rt-filter-row {
	display: flex;
	align-items: center;
	gap: 8px;
}
.rt-filter-row label {
	font-size: 12px;
	color: #64748b;
	font-weight: 600;
}
.rt-sep {
	color: #94a3b8;
}
.rt-filter-actions {
	margin-left: auto;
	display: flex;
	gap: 8px;
}

/* Dark mode */
.reporting-app.dark .rt-filter-panel {
	background: #1f1f1f;
	border-color: #404040;
}
.reporting-app.dark .rt-filter-row label {
	color: #a3a3a3;
}
.reporting-app.dark .rt-sep {
	color: #737373;
}

/* Filter Builder (chips and layout) */
.rt-filter-builder {
	display: flex;
	flex-direction: column;
	gap: 8px;
	width: 100%;
}
.rt-filter-builder-row {
	flex-wrap: wrap;
}
.rt-value {
	display: inline-flex;
	align-items: center;
	gap: 8px;
}
.rt-active-filters {
	display: flex;
	align-items: center;
	gap: 8px;
	flex-wrap: wrap;
}
.rt-chip {
	display: inline-flex;
	align-items: center;
	gap: 6px;
	padding: 4px 8px;
	border-radius: 999px;
	background: #f1f5f9;
	color: #334155;
	border: 1px solid #e2e8f0;
	font-size: 12px;
}
.rt-chip-remove {
	background: transparent;
	border: none;
	color: inherit;
	cursor: pointer;
	font-size: 14px;
	line-height: 1;
	padding: 0 2px;
}
.reporting-app.dark .rt-chip {
	background: #262626;
	color: #e5e7eb;
	border-color: #404040;
}

/* Inputs used in the dynamic filter panel */
.rt-input {
	height: 36px;
	padding: 6px 10px;
	border: 1px solid #e2e8f0;
	border-radius: 10px;
	background: #fff;
	color: #1f2937;
	outline: none;
	font-size: 13px;
	line-height: 20px;
	min-width: 120px;
}
.rt-input::placeholder {
	color: #94a3b8;
}

/* Make select inputs match size and font */
.rt-select-input {
	font-size: 13px;
}

/* Date inputs in the panel */
.rt-filter-panel input[type="date"] {
	height: 36px;
	padding: 6px 10px;
	border: 1px solid #e2e8f0;
	border-radius: 10px;
	background: #fff;
	color: #1f2937;
	font-size: 13px;
	line-height: 20px;
}

/* Dark mode for inputs */
.reporting-app.dark .rt-input,
.reporting-app.dark .rt-filter-panel input[type="date"] {
	background: #1f1f1f;
	border-color: #404040;
	color: #f5f5f5;
}
.reporting-app.dark .rt-input::placeholder {
	color: #737373;
}

.reporting-app.dark .rt-chip:hover {
	background: #2a2a2a;
}
.reporting-app.dark .rt-chip[aria-pressed="true"] {
	background: #312e81;
	color: #c7d2fe;
	border-color: #4338ca;
}
.reporting-app.dark .rt-select-label {
	color: #a3a3a3;
}
.reporting-app.dark .rt-select-input {
	background: #1f1f1f;
	border-color: #404040;
	color: #f5f5f5;
}

/* Responsive */
@media (max-width: 640px) {
	.report-toolbar {
		flex-direction: column;
		align-items: stretch;
		gap: 10px;
	}
	.report-toolbar .rt-left,
	.report-toolbar .rt-right {
		width: 100%;
	}
	.report-toolbar .rt-right {
		justify-content: flex-start;
	}
	.rt-search {
		flex: 1 1 auto;
	}
	.rt-search-input {
		width: 100%;
		min-width: 0;
	}
}

.report-data-table {
	width: 100%;
	border-collapse: collapse;
	min-width: 720px; /* give tables a comfortable min width */
	font-size: 14px; /* readable default */
	color: #1f2937;
}

.report-data-table thead th {
	position: sticky; /* keep header visible during vertical scroll */
	top: 0;
	z-index: 2;
	text-align: left;
	padding: 12px 16px;
	font-weight: 600;
	font-size: 13px;
	color: #0f172a;
	background: #f8fafc;
	border-bottom: 1px solid #e6edf3;
	white-space: nowrap; /* prevent header text from wrapping */
	overflow: hidden; /* clip overflow if column is narrow */
	text-overflow: ellipsis; /* show ellipsis when clipped */
}

.report-data-table tfoot th {
	text-align: left;
	padding: 12px 16px;
	font-weight: 600;
	font-size: 13px;
	color: #0f172a;
	background: #f8fafc;
	border-top: 1px solid #e6edf3;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.report-data-table tbody td {
	padding: 12px 16px;
	vertical-align: middle;
	color: #374151;
	border-bottom: 1px solid #eef2f7;
}

.report-data-table tbody tr:nth-child(even) {
	background: #ffffff;
}

.report-data-table tbody tr:nth-child(odd) {
	background: #fbfdff;
}

.report-data-table .report-header-cell {
	/* ensure the template class is visible if used directly */
	font-weight: 600;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.report-data-table .report-cell {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* Responsive: allow table to shrink and wrap on very small screens */
@media (max-width: 640px) {
	.report-data-table {
		min-width: 640px;
		font-size: 13px;
	}
}

/* 6. Dark Mode Overrides */
.reporting-app.dark .report-view-header .back-btn {
	background: #2a2a2a;
	border-color: #404040;
	color: #f5f5f5;
}
.reporting-app.dark .report-view-header .back-btn:hover {
	background: #404040;
}
.reporting-app.dark .report-view-header .report-breadcrumbs .crumb {
	color: #94a3b8;
}
.reporting-app.dark .report-view-header .report-breadcrumbs .crumb.current {
	color: #f1f5f9;
}
.reporting-app.dark .report-view-meta .report-icon.single-view-icon {
	background: #404040;
	border-color: #525252;
	color: #f5f5f5;
}
.reporting-app.dark .rv-title {
	color: #f1f5f9;
}
.reporting-app.dark .rv-subtitle {
	color: #a3a3a3;
}
.reporting-app.dark .summary-label {
	color: #a3a3a3;
}
.reporting-app.dark .summary-value {
	color: #f1f5f9;
}
.reporting-app.dark .report-format-tag {
	background: #312e81;
	color: #c7d2fe;
}

/* Dark mode: table-specific overrides to match the app's dark palette */
.reporting-app.dark .report-table-scroller {
	background: transparent;
}
.reporting-app.dark .report-data-table {
	color: #a3a3a3; /* body text color used elsewhere in dark mode */
}
.reporting-app.dark .report-data-table thead th,
.reporting-app.dark .report-data-table tfoot th {
	background: #2a2a2a; /* match .card/back button background used in dark mode */
	color: #f5f5f5; /* primary light text color */
	border-bottom: 1px solid #404040; /* match card border */
}
.reporting-app.dark .report-data-table tbody td {
	color: #c0c0c0; /* slightly lighter body text for improved readability */
	border-bottom: 1px solid #404040; /* visible divider but not harsh */
}
.reporting-app.dark .report-data-table tbody tr:nth-child(even) {
	background: #232323; /* subtle banding to separate rows */
}
.reporting-app.dark .report-data-table tbody tr:nth-child(odd) {
	background: transparent; /* let the card background show through */
}
.reporting-app.dark .report-data-table .report-header-cell {
	color: #f5f5f5;
}
.reporting-app.dark .report-data-table .report-cell {
	color: #c0c0c0; /* slightly lighter than before */
}

/* 7. Responsive */
@media (max-width: 1100px) {
	.report-view-body {
		flex-direction: column;
	}
	.report-summary {
		width: 100%;
		flex-direction: row;
		flex-wrap: wrap;
	}
	.report-summary .summary-card {
		flex: 1 1 160px;
	}
}
@media (max-width: 640px) {
	.report-view-meta {
		flex-direction: column;
	}
	.report-view-meta .report-icon.single-view-icon {
		width: 52px;
		height: 52px;
		font-size: 26px;
	}
	.rv-title {
		font-size: 22px;
		line-height: 28px;
	}
	.report-view-bar {
		flex-direction: column;
		align-items: flex-start;
		gap: 16px;
	}
	.report-view-header .rv-actions {
		margin-left: 0;
	}
	.report-view-header .rv-actions .generate-btn::before {
		display: none;
	}
}

/* ------------------------------------------------------------------------
   8. Single Report Dedicated Mode
   ------------------------------------------------------------------------ */
.reporting-app.single-report-mode .sidebar,
.reporting-app.single-report-mode .sidebar-toggle,
.reporting-app.single-report-mode .sidebar-backdrop,
.reporting-app.single-report-mode .category-header,
.reporting-app.single-report-mode .reports-grid,
.reporting-app.single-report-mode .empty-state {
	display: none;
}

.reporting-app.single-report-mode .main-content {
	padding: 40px clamp(20px, 4vw, 60px);
	max-width: 1600px;
	margin: 0 auto;
}

.reporting-app.single-report-mode .report-view-header {
	margin-top: 0;
}

/* Dark mode adjustments for dedicated view (reuse existing colors) */
.reporting-app.dark.single-report-mode .main-content {
	background: #1a1a1a;
}

/* ------------------------------------------------------------------------
   4a. Category Card Styles
   ------------------------------------------------------------------------ */

.category-card {
	display: flex;
	flex-direction: column;
	position: relative;
}

.category-card-header {
	display: flex;
	align-items: center;
	margin-bottom: 16px;
}

.category-icon {
	width: 48px;
	height: 48px;
	border-radius: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16px;
	font-size: 20px;
	flex-shrink: 0;
	background: #f7f8fa;
	color: #495057;
	border: 1px solid #dee2e6;
}

.category-title {
	font-size: 18px;
	margin: 0;
	color: #1a202c;
	font-weight: 600;
	line-height: 1.25;
}

.category-description {
	margin: 0 0 20px 0;
	color: #64748b;
	font-size: 14px;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

.category-meta {
	margin-top: auto;
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 12px;
}

.category-report-count {
	font-size: 12px;
	font-weight: 600;
	letter-spacing: 0.5px;
	text-transform: uppercase;
	color: #94a3b8;
}

.browse-category-btn {
	background: #6366f1;
	color: #fff;
	border: none;
	padding: 8px 18px;
	border-radius: 10px;
	font-size: 13px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.2s ease;
}

.browse-category-btn:hover {
	background: #5855eb;
}
.browse-category-btn:active {
	animation: btnBounce 0.1s ease-in-out;
}

/* Responsive tweak */
@media (max-width: 640px) {
	.relevant-categories-grid {
		grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
	}
	.category-description {
		-webkit-line-clamp: 4;
		line-clamp: 4;
	}
}

/* ------------------------------------------------------------------------
   5. Empty State
   ------------------------------------------------------------------------ */

.empty-state {
	text-align: center;
	padding: 60px 20px;
	color: #717596;
}

.empty-state h3 {
	font-size: 18px;
	margin: 0 0 8px 0;
	color: #4a5568;
}

.empty-state p {
	margin: 0;
	font-size: 14px;
}
/* ------------------------------------------------------------------------
	6. Theme Toggle & Animations
	------------------------------------------------------------------------ */

/* Theme Toggle Button */
.theme-toggle {
	position: fixed;
	top: 20px;
	right: 20px;
	background: #f8f9fa;
	border: 1px solid #dee2e6;
	border-radius: 10px;
	padding: 10px;
	cursor: pointer;
	transition: all 0.2s ease;
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 44px;
	height: 44px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-toggle:hover {
	background: #e9ecef;
}

.theme-toggle svg {
	width: 20px;
	height: 20px;
	transition: all 0.2s ease;
}

/* Button Bounce Animation */
@keyframes btnBounce {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(0.992);
	}
	100% {
		transform: scale(1);
	}
}

/* ------------------------------------------------------------------------
	7. Dark Mode Overrides
	------------------------------------------------------------------------ */

/* Dark Mode Theme Toggle */
.reporting-app.dark .theme-toggle {
	background: #404040;
	border-color: #525252;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
	color: #f5f5f5;
}

.reporting-app.dark .theme-toggle:hover {
	background: #525252;
	border-color: #737373;
}

/* Dark Mode Overrides for Shared Elements */
.reporting-app.dark {
	background: #1a1a1a;
}

.reporting-app.dark .main-content {
	background: #1a1a1a;
}

.reporting-app.dark .content-header h2 {
	color: #f5f5f5;
}

.reporting-app.dark .content-header p {
	color: #a3a3a3;
}

.reporting-app.dark .btn-primary {
	background: #6366f1;
}

.reporting-app.dark .btn-primary:hover {
	background: #5855eb;
}

.reporting-app.dark .btn-secondary {
	background: #2a2a2a;
	color: #f5f5f5;
	border: 1px solid #404040;
}

.reporting-app.dark .btn-secondary:hover {
	background: #404040;
	border-color: #525252;
}

.reporting-app.dark .refresh-btn {
	color: #a3a3a3;
	border: 1px solid #404040;
	background: transparent;
}

.reporting-app.dark .refresh-btn:hover {
	background: #2a2a2a;
	border-color: #525252;
	color: #f5f5f5;
}

.reporting-app.dark .generate-btn {
	background: #6366f1;
}

.reporting-app.dark .generate-btn:hover {
	background: #5855eb;
}

.reporting-app.dark .favorite-btn {
	background: #2a2a2a;
	border: 1px solid #404040;
	color: #a3a3a3;
}

.reporting-app.dark .favorite-btn:hover {
	background: #404040;
	border-color: #525252;
	color: #f5f5f5;
}

.reporting-app.dark .favorite-btn.favorited svg {
	fill: #f87171;
	stroke: #f87171;
}

.reporting-app.dark .card {
	background: #2a2a2a;
	border: 1px solid #404040;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.reporting-app.dark .card:hover {
	border-color: #525252;
}

.reporting-app.dark .section-header h2 {
	color: #f5f5f5;
}

.reporting-app.dark .section-description {
	color: #a3a3a3;
}

.reporting-app.dark .empty-state {
	color: #a3a3a3;
}

.reporting-app.dark .empty-state h3 {
	color: #f5f5f5;
}

/* Dark Report Cards */
.reporting-app.dark .report-card {
	/* Uses shared .card dark styles */
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.reporting-app.dark .report-icon {
	background: #404040;
	color: #f5f5f5;
	border: 1px solid #525252;
}

.reporting-app.dark .report-content h3 {
	color: #f5f5f5;
}

.reporting-app.dark .report-content p {
	color: #a3a3a3;
}

.reporting-app.dark .report-content p.no-description {
	color: #737373;
}

/* Dark Mode Category Cards */
.reporting-app.dark .category-icon {
	background: #404040;
	color: #f5f5f5;
	border: 1px solid #525252;
}

.reporting-app.dark .category-title {
	color: #f5f5f5;
}
.reporting-app.dark .category-description {
	color: #a3a3a3;
}
.reporting-app.dark .category-report-count {
	color: #737373;
}
.reporting-app.dark .browse-category-btn {
	background: #6366f1;
}
.reporting-app.dark .browse-category-btn:hover {
	background: #5855eb;
}

/* ------------------------------------------------------------------------
   9. Table: Dynamic height to viewport under report header
   ------------------------------------------------------------------------ */
/* Make the report body consume remaining viewport height under the header */
.report-view-body {
	flex: 1 1 auto;
	min-height: 0; /* allow children to size correctly in flex column */
}

/* Ensure the data area and wrapper form a flex chain for the scroller */
.report-data-area {
	display: flex;
	flex-direction: column;
	flex: 1 1 auto;
	min-height: 0;
}

.report-table-wrapper {
	display: flex;
	flex-direction: column;
	flex: 1 1 auto;
	min-height: 0;
}

/* Let the scroller fill the available height dynamically */
.report-table-scroller {
	width: 100%;
	flex: 0 0 auto; /* respect explicit viewport height */
	height: 75vh; /* viewport-based height */
	max-height: 75vh;
	min-height: 0;
	overflow-y: auto;
	overflow-x: scroll; /* keep horizontal scrollbar visible */
}
